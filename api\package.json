{"name": "ccpalcan-api", "version": "2.5.0", "private": true, "description": "api para la app, ccpalcan", "scripts": {"build": "strapi build", "build:clean": "rm -rf build dist .strapi && strapi build", "clean": "rm -rf build dist .strapi", "consents:report": "node ./scripts/run-consent-report.js", "consents:retroactive": "node ./scripts/create-consents-simple.js", "db:clean": "node ./scripts/clean-database.js", "db:reset": "npm run db:clean && npm run db:seed && npm run db:seed:email-templates", "db:seed": "npm run db:seed:roles && npm run db:seed:users && npm run db:seed:blocks && npm run db:seed:warning-rules && npm run db:seed:complaint-templates", "db:seed:blocks": "node ./scripts/seed-blocks-db.js", "db:seed:complaint-templates": "node ./scripts/seed-complaint-templates.js", "db:seed:email-templates": "npx tsc && node ./dist/src/scripts/setup-email-templates.js", "db:seed:roles": "node ./scripts/seed-roles-db.js", "db:seed:users": "node ./scripts/seed-users-db.js", "db:seed:warning-rules": "node ./scripts/seed-warning-rules-db.js", "develop": "strapi develop", "postinstall": "if [ \"$FORCE_CLEAN_BUILD\" = \"true\" ]; then rm -rf build dist .strapi; fi", "start": "strapi start", "strapi": "strapi", "test:email": "node ./src/scripts/test/send-test-email.js"}, "dependencies": {"@sendgrid/mail": "^7.7.0", "@strapi/email": "5.0.0", "@strapi/plugin-cloud": "5.0.0", "@strapi/plugin-documentation": "5.0.0", "@strapi/plugin-users-permissions": "5.0.0", "@strapi/provider-email-sendgrid": "5.0.0", "@strapi/provider-upload-cloudinary": "5.0.0", "@strapi/strapi": "5.0.0", "dotenv": "^16.4.7", "ejs": "^3.1.10", "fs-extra": "^10.0.0", "mime-types": "^2.1.27", "mysql2": "^3.14.1", "openai": "^4.103.0", "react": "^18.0.0", "react-dom": "^18.0.0", "react-router-dom": "^6.0.0", "styled-components": "^6.0.0", "web-push": "^3.6.7", "ws": "^8.18.2"}, "devDependencies": {"@types/node": "^20.17.57", "@types/react": "^18", "@types/react-dom": "^18", "typescript": "^5"}, "engines": {"node": "20.x.x", "npm": ">=6.0.0"}, "strapi": {"uuid": "************************************"}}