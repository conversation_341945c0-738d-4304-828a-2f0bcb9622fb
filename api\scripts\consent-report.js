/**
 * Script para generar un reporte de consentimientos
 * Muestra qué usuarios tienen y no tienen consentimientos
 */

const mysql = require("mysql2/promise");
require("dotenv").config();

// Configuración de la base de datos
const dbConfig = {
  host: process.env.DATABASE_HOST || "localhost",
  port: parseInt(process.env.DATABASE_PORT, 10) || 3306,
  user: process.env.DATABASE_USERNAME,
  password: process.env.DATABASE_PASSWORD,
  database: process.env.DATABASE_NAME,
};

const generateConsentReport = async () => {
  let connection;
  try {
    console.log("📊 Generando reporte de consentimientos...\n");

    // Conectar a la base de datos
    connection = await mysql.createConnection(dbConfig);

    // 1. Obtener todos los usuarios
    const [users] = await connection.execute(`
      SELECT
        id,
        username,
        email,
        created_at
      FROM up_users
      ORDER BY id
    `);

    // 2. Verificar qué tablas existen
    const [tables] = await connection.execute(`SHOW TABLES`);
    console.log(
      "📋 Tablas disponibles:",
      tables.map((t) => Object.values(t)[0])
    );

    // 3. Verificar estructura de la tabla de consentimientos
    const consentTableName = tables.find((t) =>
      Object.values(t)[0].toLowerCase().includes("consent")
    );

    if (consentTableName) {
      const tableName = Object.values(consentTableName)[0];
      console.log(`🔍 Estructura de la tabla ${tableName}:`);
      const [columns] = await connection.execute(`DESCRIBE ${tableName}`);
      columns.forEach((col) => console.log(`   - ${col.Field} (${col.Type})`));
    }

    // Obtener consentimientos con información de usuarios
    const [consents] = await connection.execute(`
      SELECT
        c.id,
        c.fecha_aceptacion,
        c.tipo_consentimiento,
        c.activo,
        cul.user_id,
        u.username,
        u.email
      FROM consents c
      INNER JOIN consents_user_lnk cul ON c.id = cul.consent_id
      INNER JOIN up_users u ON cul.user_id = u.id
      WHERE c.activo = 1
      ORDER BY c.fecha_aceptacion DESC
    `);

    // 3. Crear mapas para análisis
    const usersWithConsents = new Map();
    const consentsByType = new Map();

    consents.forEach((consent) => {
      if (consent.user_id) {
        if (!usersWithConsents.has(consent.user_id)) {
          usersWithConsents.set(consent.user_id, []);
        }
        usersWithConsents.get(consent.user_id).push(consent);

        // Contar por tipo
        const type = consent.tipo_consentimiento;
        consentsByType.set(type, (consentsByType.get(type) || 0) + 1);
      }
    });

    // 4. Separar usuarios con y sin consentimientos
    const usersWithoutConsents = users.filter(
      (user) => !usersWithConsents.has(user.id)
    );

    const usersWithConsentsList = users.filter((user) =>
      usersWithConsents.has(user.id)
    );

    // 5. Generar reporte
    console.log("═══════════════════════════════════════════════════════════");
    console.log("                    REPORTE DE CONSENTIMIENTOS");
    console.log("═══════════════════════════════════════════════════════════\n");

    console.log("📈 ESTADÍSTICAS GENERALES:");
    console.log(`   Total de usuarios: ${users.length}`);
    console.log(`   Usuarios CON consentimientos: ${usersWithConsentsList.length}`);
    console.log(`   Usuarios SIN consentimientos: ${usersWithoutConsents.length}`);
    console.log(`   Total de consentimientos: ${consents.length}\n`);

    console.log("📊 CONSENTIMIENTOS POR TIPO:");
    consentsByType.forEach((count, type) => {
      console.log(`   ${type}: ${count}`);
    });
    console.log("");

    if (usersWithoutConsents.length > 0) {
      console.log("❌ USUARIOS SIN CONSENTIMIENTOS:");
      console.log(
        "   ID  | Username           | Email                    | Fecha Registro"
      );
      console.log(
        "   ----|--------------------|--------------------------|------------------"
      );

      usersWithoutConsents.forEach((user) => {
        const id = String(user.id).padEnd(3);
        const username = (user.username || "N/A").padEnd(18);
        const email = (user.email || "N/A").padEnd(24);
        const createdAt = user.created_at
          ? new Date(user.created_at).toLocaleDateString()
          : "N/A";

        console.log(`   ${id} | ${username} | ${email} | ${createdAt}`);
      });
      console.log("");
    }

    if (usersWithConsentsList.length > 0) {
      console.log("✅ USUARIOS CON CONSENTIMIENTOS:");
      console.log(
        "   ID  | Username           | Email                    | Consentimientos"
      );
      console.log(
        "   ----|--------------------|--------------------------|------------------"
      );

      usersWithConsentsList.forEach((user) => {
        const id = String(user.id).padEnd(3);
        const username = (user.username || "N/A").padEnd(18);
        const email = (user.email || "N/A").padEnd(24);
        const userConsents = usersWithConsents.get(user.id) || [];
        const consentTypes = userConsents
          .map((c) => c.tipo_consentimiento)
          .join(", ");

        console.log(`   ${id} | ${username} | ${email} | ${consentTypes}`);
      });
      console.log("");
    }

    console.log("═══════════════════════════════════════════════════════════");

    if (usersWithoutConsents.length > 0) {
      console.log("💡 RECOMENDACIÓN:");
      console.log(
        "   Ejecuta el siguiente comando para crear consentimientos retroactivos:"
      );
      console.log("   npm run consents:retroactive");
    } else {
      console.log("🎉 ¡Perfecto! Todos los usuarios tienen consentimientos.");
    }

    console.log("═══════════════════════════════════════════════════════════\n");
  } catch (error) {
    console.error("💥 Error generando el reporte:", error);
    throw error;
  } finally {
    if (connection) {
      await connection.end();
    }
  }
};

module.exports = {
  generateConsentReport,
};
