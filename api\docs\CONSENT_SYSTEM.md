# Sistema de Consentimientos - Documentación

## 📋 Descripción General

El sistema de consentimientos de CC Palmas del Cacique está diseñado para cumplir con las regulaciones de protección de datos (GDPR, LOPD, etc.) registrando automáticamente el consentimiento de los usuarios para el tratamiento de sus datos personales.

## 🏗️ Arquitectura del Sistema

### Content Type: Consent
- **Ubicación**: `api/src/api/consent/`
- **Tabla de BD**: `consents` + `consents_user_lnk` (relación con usuarios)
- **Campos principales**:
  - `user`: Relación con el usuario
  - `fecha_aceptacion`: Fecha y hora de aceptación
  - `ip_address`: Dirección IP desde donde se aceptó
  - `user_agent`: Información del navegador
  - `version_politica`: Versión de la política de privacidad
  - `tipo_consentimiento`: Tipo de consentimiento (registro, marketing, etc.)
  - `activo`: Estado del consentimiento

### Controlador de Consentimientos
- **Ubicación**: `api/src/api/consent/controllers/consent.ts`
- **Endpoints disponibles**:
  - `GET /api/consents` - Listar consentimientos
  - `POST /api/consents` - Crear consentimiento
  - `GET /api/consents/:id` - Obtener consentimiento específico
  - `GET /api/consents/user/:userId` - Consentimientos de un usuario
  - `POST /api/consents/accept` - Aceptar consentimiento (público)
  - `GET /api/consents/export` - Exportar consentimientos (admin)

## 🔄 Flujo Automático de Registro

### 1. Registro de Nuevos Usuarios
Cuando un usuario se registra a través de `/api/auth/local/register`:

1. **Hook automático** en `api/src/extensions/users-permissions/strapi-server.ts`
2. **Intercepta** el controlador `auth.register`
3. **Crea automáticamente** un consentimiento con:
   - Fecha actual como fecha de aceptación
   - IP del usuario
   - User agent del navegador
   - Versión de política "1.0"
   - Tipo "registro"
   - Estado activo

### 2. Código del Hook
```typescript
// Extender el controlador de registro para crear consentimientos automáticamente
const originalRegister = plugin.controllers.auth.register;
plugin.controllers.auth.register = async (ctx) => {
  await originalRegister(ctx);

  if (ctx.response.status === 200 && ctx.response.body?.user?.id) {
    try {
      const userId = ctx.response.body.user.id;
      const ip_address = /* lógica de IP */;
      const user_agent = ctx.request.header["user-agent"] || "unknown";

      await strapi.entityService.create("api::consent.consent", {
        data: {
          user: userId,
          fecha_aceptacion: new Date().toISOString(),
          ip_address,
          user_agent,
          version_politica: "1.0",
          tipo_consentimiento: "registro",
          activo: true,
        },
      });
    } catch (error) {
      console.warn("Error al crear consentimiento automático:", error);
    }
  }
};
```

## 🛠️ Scripts de Gestión

### 1. Reporte de Consentimientos
```bash
npm run consents:report
```

**Funcionalidad**:
- Muestra estadísticas generales
- Lista usuarios con y sin consentimientos
- Cuenta consentimientos por tipo
- Proporciona recomendaciones

**Archivo**: `api/scripts/consent-report.js`

### 2. Consentimientos Retroactivos
```bash
npm run consents:retroactive
```

**Funcionalidad**:
- Identifica usuarios sin consentimientos
- Crea consentimientos retroactivos automáticamente
- Usa la fecha de registro del usuario
- Marca como "retroactive" y "system-generated"

**Archivo**: `api/scripts/create-consents-simple.js`

## 📊 Tipos de Consentimiento

### Tipos Implementados
- **`registro`**: Consentimiento básico para el registro de usuario
- **`marketing`**: Para comunicaciones promocionales (futuro)
- **`cookies`**: Para el uso de cookies (futuro)
- **`actualizacion_politica`**: Cuando se actualicen los términos (futuro)

### Versiones de Política
- **`1.0`**: Versión inicial de la política de privacidad

## 🔒 Cumplimiento Legal

### Datos Registrados para Auditoría
- ✅ **Quién**: ID del usuario
- ✅ **Qué**: Tipo de consentimiento
- ✅ **Cuándo**: Fecha y hora exacta
- ✅ **Dónde**: Dirección IP
- ✅ **Cómo**: User agent del navegador
- ✅ **Versión**: Versión de la política aceptada

### Características de Cumplimiento
- **Trazabilidad completa**: Cada consentimiento tiene metadatos completos
- **Inmutabilidad**: Los consentimientos no se modifican, solo se crean nuevos
- **Exportación**: Endpoint para exportar datos de consentimientos
- **Revocación**: Sistema preparado para manejar revocación de consentimientos

## 🚀 Uso en Producción

### Verificación Regular
```bash
# Ejecutar semanalmente para verificar estado
npm run consents:report
```

### Monitoreo de Nuevos Usuarios
El sistema automático se encarga de crear consentimientos para nuevos registros.

### Backup de Consentimientos
Los consentimientos se almacenan en la base de datos principal y se incluyen en los backups regulares.

## 🔧 Mantenimiento

### Actualización de Política de Privacidad
Cuando se actualice la política:
1. Cambiar la versión en el hook automático
2. Implementar notificación a usuarios existentes
3. Crear endpoint para re-consentimiento

### Nuevos Tipos de Consentimiento
Para agregar nuevos tipos:
1. Actualizar el enum en el schema del content type
2. Crear endpoints específicos si es necesario
3. Actualizar la documentación

## 📈 Métricas y Reportes

### Estadísticas Disponibles
- Total de usuarios registrados
- Usuarios con/sin consentimientos
- Distribución por tipo de consentimiento
- Consentimientos por fecha

### Exportación de Datos
Endpoint disponible para exportar consentimientos en formato JSON para auditorías.

## ⚠️ Consideraciones Importantes

1. **No eliminar consentimientos**: Para mantener trazabilidad legal
2. **Backup regular**: Los consentimientos son datos críticos legales
3. **Monitoreo**: Verificar que el hook automático funcione correctamente
4. **Actualizaciones**: Mantener versiones de política actualizadas

---

**Última actualización**: Enero 2025  
**Versión del sistema**: 1.0  
**Estado**: ✅ Producción - Completamente funcional
