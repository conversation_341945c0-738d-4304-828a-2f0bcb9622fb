import type { Struct, Schema } from '@strapi/strapi';

export interface WarningSanctionBy extends Struct.ComponentSchema {
  collectionName: 'components_warning_sanction_by';
  info: {
    displayName: 'SanctionBy';
    description: 'Informaci\u00F3n del usuario que crea la sanci\u00F3n';
    icon: 'user';
  };
  attributes: {
    firstName: Schema.Attribute.String & Schema.Attribute.Required;
    lastName: Schema.Attribute.String & Schema.Attribute.Required;
    role: Schema.Attribute.String & Schema.Attribute.Required;
    userId: Schema.Attribute.Integer & Schema.Attribute.Required;
  };
}

export interface WarningRule extends Struct.ComponentSchema {
  collectionName: 'components_warning_rules';
  info: {
    displayName: 'Rule';
    description: 'Regla o art\u00EDculo del reglamento infringido';
  };
  attributes: {
    article: Schema.Attribute.String & Schema.Attribute.Required;
    description: Schema.Attribute.Text & Schema.Attribute.Required;
    chapter: Schema.Attribute.String & Schema.Attribute.Required;
  };
}

export interface WarningNotificationConfig extends Struct.ComponentSchema {
  collectionName: 'components_warning_notification_configs';
  info: {
    displayName: 'NotificationConfig';
    description: 'Configuraci\u00F3n de notificaciones para advertencias';
  };
  attributes: {
    enabled: Schema.Attribute.Boolean & Schema.Attribute.DefaultTo<true>;
    channels: Schema.Attribute.Component<
      'warning.notification-channels',
      false
    >;
  };
}

export interface WarningNotificationChannels extends Struct.ComponentSchema {
  collectionName: 'components_warning_notification_channels';
  info: {
    displayName: 'NotificationChannels';
    description: 'Canales disponibles para env\u00EDo de notificaciones';
  };
  attributes: {
    email: Schema.Attribute.Boolean & Schema.Attribute.DefaultTo<true>;
    whatsapp: Schema.Attribute.Boolean & Schema.Attribute.DefaultTo<false>;
  };
}

export interface SharedSlider extends Struct.ComponentSchema {
  collectionName: 'components_shared_sliders';
  info: {
    displayName: 'Slider';
    icon: 'address-book';
    description: '';
  };
  attributes: {
    files: Schema.Attribute.Media<'images', true>;
  };
}

export interface SharedSeo extends Struct.ComponentSchema {
  collectionName: 'components_shared_seos';
  info: {
    name: 'Seo';
    icon: 'allergies';
    displayName: 'Seo';
    description: '';
  };
  attributes: {
    metaTitle: Schema.Attribute.String & Schema.Attribute.Required;
    metaDescription: Schema.Attribute.Text & Schema.Attribute.Required;
    shareImage: Schema.Attribute.Media<'images'>;
  };
}

export interface SharedRichText extends Struct.ComponentSchema {
  collectionName: 'components_shared_rich_texts';
  info: {
    displayName: 'Rich text';
    icon: 'align-justify';
    description: '';
  };
  attributes: {
    body: Schema.Attribute.RichText;
  };
}

export interface SharedQuote extends Struct.ComponentSchema {
  collectionName: 'components_shared_quotes';
  info: {
    displayName: 'Quote';
    icon: 'indent';
  };
  attributes: {
    title: Schema.Attribute.String;
    body: Schema.Attribute.Text;
  };
}

export interface SharedMedia extends Struct.ComponentSchema {
  collectionName: 'components_shared_media';
  info: {
    displayName: 'Media';
    icon: 'file-video';
  };
  attributes: {
    file: Schema.Attribute.Media<'images' | 'files' | 'videos'>;
  };
}

export interface VisitVisitor extends Struct.ComponentSchema {
  collectionName: 'components_visit_visitors';
  info: {
    displayName: 'Visitor';
    description: 'Informaci\u00F3n del visitante';
  };
  attributes: {
    first_name: Schema.Attribute.String & Schema.Attribute.Required;
    last_name: Schema.Attribute.String & Schema.Attribute.Required;
    document_number: Schema.Attribute.String & Schema.Attribute.Required;
    photo: Schema.Attribute.Media<'images'>;
  };
}

export interface VisitVehicle extends Struct.ComponentSchema {
  collectionName: 'components_visit_vehicles';
  info: {
    displayName: 'Vehicle';
    description: 'Informaci\u00F3n del veh\u00EDculo del visitante';
  };
  attributes: {
    license_plate: Schema.Attribute.String & Schema.Attribute.Required;
    brand: Schema.Attribute.String;
    model: Schema.Attribute.String;
    color: Schema.Attribute.String;
    photo: Schema.Attribute.Media<'images'>;
  };
}

export interface CommonGuest extends Struct.ComponentSchema {
  collectionName: 'components_common_guests';
  info: {
    displayName: 'Guest';
    description: 'Invitado para una reservaci\u00F3n';
    icon: 'user';
  };
  attributes: {
    name: Schema.Attribute.String &
      Schema.Attribute.Required &
      Schema.Attribute.SetMinMaxLength<{
        minLength: 2;
      }>;
    lastName: Schema.Attribute.String &
      Schema.Attribute.Required &
      Schema.Attribute.SetMinMaxLength<{
        minLength: 2;
      }>;
    isConfirmed: Schema.Attribute.Boolean & Schema.Attribute.DefaultTo<false>;
  };
}

declare module '@strapi/strapi' {
  export module Public {
    export interface ComponentSchemas {
      'warning.sanction-by': WarningSanctionBy;
      'warning.rule': WarningRule;
      'warning.notification-config': WarningNotificationConfig;
      'warning.notification-channels': WarningNotificationChannels;
      'shared.slider': SharedSlider;
      'shared.seo': SharedSeo;
      'shared.rich-text': SharedRichText;
      'shared.quote': SharedQuote;
      'shared.media': SharedMedia;
      'visit.visitor': VisitVisitor;
      'visit.vehicle': VisitVehicle;
      'common.guest': CommonGuest;
    }
  }
}
