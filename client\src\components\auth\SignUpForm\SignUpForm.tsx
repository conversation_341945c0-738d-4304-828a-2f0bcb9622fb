import { useState } from "react";
import { useNavigate, Link } from "react-router-dom";
import { useTranslation } from "react-i18next";
import { Flex } from "antd";

import { doSignUp } from "store/slices/authSlice";
import { useAppDispatch } from "hooks/reduxHooks";
import { BaseForm } from "components/common/forms/BaseForm/BaseForm";
import { BaseModal } from "components/common/BaseModal/BaseModal";
import * as Auth from "components/layouts/AuthLayout/AuthLayout.styles";
import { useFeedback } from "@app/hooks/useFeedback";
import { SignUpRequest } from "@app/api/auth.api";

import TermAndPolicy from "./TermAndPolicy";
import * as S from "./SignUpForm.styles";

export const SignUpForm = () => {
  const navigate = useNavigate();
  const dispatch = useAppDispatch();
  const { t } = useTranslation();
  const [open, setOpen] = useState(false);
  const [isAccept, setIsAccept] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  const show = () => setOpen(true);

  const onClose = () => setOpen(false);

  const onOk = () => {
    setIsAccept(true);
    onClose();
  };

  const onCancel = () => {
    setIsAccept(false);
    onClose();
  };

  const { notification } = useFeedback();

  const handleSubmit = async (payload: SignUpRequest) => {
    if (!isAccept) {
      notification.error({
        message: "Error",
        description: "Por favor, acepta los términos y condiciones",
      });
      return;
    }

    // Generar username a partir del email
    const username = payload.email.split("@")[0];
    const finalPayload = {
      ...payload,
      username,
    };

    setIsLoading(true);

    try {
      const response = await dispatch(doSignUp(finalPayload)).unwrap();

      notification.success({
        message: t("auth.signUpSuccessMessage"),
        description: t("auth.signUpSuccessDescription"),
      });
      navigate("/auth/login");
    } catch (err: any) {
      let errorMessage = "Ha ocurrido un error al registrarse";

      // Manejar errores específicos
      if (err.message.includes("Email or Username are already taken")) {
        errorMessage =
          "El correo electrónico ya está registrado. Por favor utiliza otro correo.";
      } else if (err.message.includes("Invalid identifier or password")) {
        errorMessage = "El correo electrónico o la contraseña son inválidos";
      }

      notification.error({
        message: "Error de registro",
        description: errorMessage,
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <>
      <Auth.FormWrapper>
        <BaseForm
          layout="vertical"
          onFinish={handleSubmit}
          requiredMark="optional"
          disabled={isLoading}
        >
          <Auth.BackWrapper onClick={() => navigate(-1)}>
            <Auth.BackIcon />
            {t("common.back")}
          </Auth.BackWrapper>
          <S.Title>{t("common.signUp")}</S.Title>
          <Auth.FormItem
            name="firstName"
            label={t("common.firstName")}
            rules={[{ required: true, message: t("common.requiredField") }]}
          >
            <Auth.FormInput placeholder={t("common.firstName")} />
          </Auth.FormItem>
          <Auth.FormItem
            name="lastName"
            label={t("common.lastName")}
            rules={[{ required: true, message: t("common.requiredField") }]}
          >
            <Auth.FormInput placeholder={t("common.lastName")} />
          </Auth.FormItem>
          <Auth.FormItem
            name="email"
            label={t("common.email")}
            rules={[
              { required: true, message: t("common.requiredField") },
              {
                type: "email",
                message: t("common.notValidEmail"),
              },
            ]}
          >
            <Auth.FormInput placeholder={t("common.email")} />
          </Auth.FormItem>
          <Auth.FormItem
            label={t("common.password")}
            name="password"
            rules={[{ required: true, message: t("common.requiredField") }]}
          >
            <Auth.FormInputPassword placeholder={t("common.password")} />
          </Auth.FormItem>
          <Auth.FormItem
            label={t("common.confirmPassword")}
            name="confirmPassword"
            dependencies={["password"]}
            rules={[
              { required: true, message: t("common.requiredField") },
              ({ getFieldValue }) => ({
                validator(_, value) {
                  if (!value || getFieldValue("password") === value) {
                    return Promise.resolve();
                  }
                  return Promise.reject(
                    new Error(t("common.confirmPasswordError")),
                  );
                },
              }),
            ]}
          >
            <Auth.FormInputPassword placeholder={t("common.confirmPassword")} />
          </Auth.FormItem>
          <Auth.ActionsWrapper>
            <BaseForm.Item
              name="termOfUse"
              valuePropName="checked"
              rules={[
                {
                  validator: (_, value) => {
                    if (!value || !isAccept) {
                      return Promise.reject(
                        new Error(
                          "Por favor, acepta los términos y condiciones",
                        ),
                      );
                    }
                    return Promise.resolve();
                  },
                },
              ]}
            >
              <Flex gap={12}>
                <Auth.FormCheckbox
                  checked={isAccept}
                  onChange={(e) => setIsAccept(e.target.checked)}
                />
                <Auth.Text>
                  {t("signup.agree")}{" "}
                  <Auth.LinkText onClick={show}>
                    {t("signup.termOfUse")}
                    {t("signup.and")} {t("signup.privacyOPolicy")}
                  </Auth.LinkText>
                </Auth.Text>
              </Flex>
            </BaseForm.Item>
          </Auth.ActionsWrapper>
          <BaseForm.Item noStyle>
            <Auth.SubmitButton
              type="primary"
              htmlType="submit"
              loading={isLoading}
            >
              {t("common.signUp")}
            </Auth.SubmitButton>
          </BaseForm.Item>
          <Auth.FooterWrapper>
            <Auth.Text>
              {t("signup.alreadyHaveAccount")}{" "}
              <Link to="/auth/login">
                <Auth.LinkText>{t("common.here")}</Auth.LinkText>
              </Link>
            </Auth.Text>
          </Auth.FooterWrapper>
        </BaseForm>
      </Auth.FormWrapper>
      <BaseModal
        open={open}
        title="Por favor leer"
        onCancel={onCancel}
        onOk={onOk}
        destroyOnClose
        width="90%"
        style={{
          maxWidth: "600px",
          top: 20,
        }}
        bodyStyle={{
          maxHeight: "calc(100vh - 200px)",
          overflowY: "auto",
          padding: "20px",
        }}
      >
        <TermAndPolicy />
      </BaseModal>
    </>
  );
};
