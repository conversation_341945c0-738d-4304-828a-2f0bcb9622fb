/**
 * Configuración del Sistema de Consentimientos
 * CC Palmas del Cacique
 */

module.exports = {
  // Configuración de versiones de política
  policyVersions: {
    current: "1.0",
    supported: ["1.0"],
  },

  // Tipos de consentimiento disponibles
  consentTypes: {
    REGISTRO: "registro",
    MARKETING: "marketing", 
    COOKIES: "cookies",
    ACTUALIZACION_POLITICA: "actualizacion_politica",
  },

  // Configuración de consentimientos automáticos
  autoConsent: {
    enabled: true,
    defaultType: "registro",
    defaultVersion: "1.0",
    retroactiveMarkers: {
      ipAddress: "retroactive",
      userAgent: "system-generated",
    },
  },

  // Configuración de exportación
  export: {
    formats: ["json", "csv"],
    includeMetadata: true,
    dateFormat: "YYYY-MM-DD HH:mm:ss",
  },

  // Configuración de auditoría
  audit: {
    logLevel: "info",
    includeUserAgent: true,
    includeIpAddress: true,
    retentionPeriod: "7 years", // Cumplimiento GDPR
  },

  // Mensajes del sistema
  messages: {
    es: {
      consentCreated: "Consentimiento registrado exitosamente",
      consentExists: "El usuario ya tiene un consentimiento activo",
      consentRevoked: "Consentimiento revocado",
      exportGenerated: "Exportación de consentimientos generada",
    },
  },

  // Configuración de base de datos
  database: {
    tables: {
      consents: "consents",
      users: "up_users",
      consentUserLink: "consents_user_lnk",
    },
    fields: {
      consent: {
        id: "id",
        documentId: "document_id",
        fechaAceptacion: "fecha_aceptacion",
        ipAddress: "ip_address",
        userAgent: "user_agent",
        versionPolitica: "version_politica",
        tipoConsentimiento: "tipo_consentimiento",
        activo: "activo",
        createdAt: "created_at",
        updatedAt: "updated_at",
        publishedAt: "published_at",
      },
      user: {
        id: "id",
        username: "username",
        email: "email",
        createdAt: "created_at",
      },
      link: {
        id: "id",
        consentId: "consent_id",
        userId: "user_id",
      },
    },
  },
};
