"use strict";
/**
 * consent controller
 */
Object.defineProperty(exports, "__esModule", { value: true });
const strapi_1 = require("@strapi/strapi");
exports.default = strapi_1.factories.createCoreController("api::consent.consent", ({ strapi }) => ({
    // Obtener todos los consentimientos (solo admin)
    async find(ctx) {
        var _a, _b;
        try {
            const user = ctx.state.user;
            // Verificar si es admin
            const isAdmin = ((_a = user.role) === null || _a === void 0 ? void 0 : _a.type) === "admin" || ((_b = user.role) === null || _b === void 0 ? void 0 : _b.name) === "Admin";
            if (!isAdmin) {
                return ctx.forbidden("Solo los administradores pueden ver todos los consentimientos");
            }
            const consents = await strapi.entityService.findMany("api::consent.consent", {
                populate: {
                    user: {
                        fields: ["id", "username", "firstName", "lastName", "email"],
                    },
                },
                sort: { fecha_aceptacion: "desc" },
            });
            return { data: consents };
        }
        catch (error) {
            console.error("Error al obtener consentimientos:", error);
            return ctx.badRequest("Error al obtener consentimientos");
        }
    },
    // Obtener un consentimiento específico
    async findOne(ctx) {
        var _a, _b, _c;
        try {
            const { id } = ctx.params;
            const user = ctx.state.user;
            const consent = await strapi.entityService.findOne("api::consent.consent", id, {
                populate: {
                    user: {
                        fields: ["id", "username", "firstName", "lastName", "email"],
                    },
                },
            });
            if (!consent) {
                return ctx.notFound("Consentimiento no encontrado");
            }
            // Solo el propietario o admin puede ver el consentimiento
            const isAdmin = ((_a = user.role) === null || _a === void 0 ? void 0 : _a.type) === "admin" || ((_b = user.role) === null || _b === void 0 ? void 0 : _b.name) === "Admin";
            const isOwner = ((_c = consent.user) === null || _c === void 0 ? void 0 : _c.id) === user.id;
            if (!isAdmin && !isOwner) {
                return ctx.forbidden("No tienes permiso para ver este consentimiento");
            }
            return { data: consent };
        }
        catch (error) {
            console.error("Error al obtener consentimiento:", error);
            return ctx.badRequest("Error al obtener consentimiento");
        }
    },
    // Crear un nuevo consentimiento
    async create(ctx) {
        try {
            const user = ctx.state.user;
            const data = ctx.request.body;
            // Obtener información del request
            const ip_address = Array.isArray(ctx.request.header["x-forwarded-for"])
                ? ctx.request.header["x-forwarded-for"][0]
                : ctx.request.header["x-forwarded-for"] || ctx.request.ip || "unknown";
            const user_agent = ctx.request.header["user-agent"] || "unknown";
            const consentData = {
                ...data,
                user: user.id,
                fecha_aceptacion: new Date().toISOString(),
                ip_address,
                user_agent,
                version_politica: data.version_politica || "1.0",
                activo: true,
            };
            const consent = await strapi.entityService.create("api::consent.consent", {
                data: consentData,
                populate: {
                    user: {
                        fields: ["id", "username", "firstName", "lastName"],
                    },
                },
            });
            return { data: consent };
        }
        catch (error) {
            console.error("Error al crear consentimiento:", error);
            return ctx.badRequest("Error al crear consentimiento");
        }
    },
    // Obtener consentimientos del usuario actual
    async findMine(ctx) {
        try {
            const user = ctx.state.user;
            const consents = await strapi.entityService.findMany("api::consent.consent", {
                filters: {
                    user: {
                        id: user.id,
                    },
                },
                sort: { fecha_aceptacion: "desc" },
            });
            return { data: consents };
        }
        catch (error) {
            console.error("Error al obtener mis consentimientos:", error);
            return ctx.badRequest("Error al obtener consentimientos");
        }
    },
    // Aceptar términos y condiciones (crear consentimiento específico)
    async acceptTerms(ctx) {
        try {
            const user = ctx.state.user;
            const { tipo_consentimiento, version_politica } = ctx.request.body;
            // Validar tipo de consentimiento
            const validTypes = [
                "registro",
                "actualizacion_politica",
                "marketing",
                "cookies",
            ];
            if (!validTypes.includes(tipo_consentimiento)) {
                return ctx.badRequest("Tipo de consentimiento inválido");
            }
            // Desactivar consentimientos anteriores del mismo tipo
            await strapi.db.query("api::consent.consent").updateMany({
                where: {
                    user: user.id,
                    tipo_consentimiento,
                    activo: true,
                },
                data: {
                    activo: false,
                },
            });
            // Obtener información del request
            const ip_address = Array.isArray(ctx.request.header["x-forwarded-for"])
                ? ctx.request.header["x-forwarded-for"][0]
                : ctx.request.header["x-forwarded-for"] || ctx.request.ip || "unknown";
            const user_agent = ctx.request.header["user-agent"] || "unknown";
            // Crear nuevo consentimiento
            const consentData = {
                user: user.id,
                fecha_aceptacion: new Date().toISOString(),
                ip_address,
                user_agent,
                version_politica: version_politica || "1.0",
                tipo_consentimiento,
                activo: true,
            };
            const consent = await strapi.entityService.create("api::consent.consent", {
                data: consentData,
                populate: {
                    user: {
                        fields: ["id", "username", "firstName", "lastName"],
                    },
                },
            });
            return {
                data: consent,
                message: "Consentimiento registrado exitosamente",
            };
        }
        catch (error) {
            console.error("Error al aceptar términos:", error);
            return ctx.badRequest("Error al registrar consentimiento");
        }
    },
    // Verificar si el usuario tiene consentimiento activo
    async checkConsent(ctx) {
        try {
            const user = ctx.state.user;
            const { tipo_consentimiento } = ctx.query;
            const filters = {
                user: {
                    id: user.id,
                },
                activo: true,
            };
            if (tipo_consentimiento) {
                filters.tipo_consentimiento = tipo_consentimiento;
            }
            const consents = await strapi.entityService.findMany("api::consent.consent", {
                filters,
                sort: { fecha_aceptacion: "desc" },
            });
            const hasActiveConsent = consents.length > 0;
            const latestConsent = hasActiveConsent ? consents[0] : null;
            return {
                data: {
                    hasActiveConsent,
                    latestConsent,
                    consentCount: consents.length,
                },
            };
        }
        catch (error) {
            console.error("Error al verificar consentimiento:", error);
            return ctx.badRequest("Error al verificar consentimiento");
        }
    },
}));
