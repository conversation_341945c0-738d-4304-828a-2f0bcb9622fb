# Scripts de Gestión de Consentimientos

## 📋 Descripción

Scripts para gestionar y monitorear el sistema de consentimientos de CC Palmas del Cacique.

## 🛠️ Scripts Disponibles

### 1. Reporte de Consentimientos

```bash
npm run consents:report
```

**Archivo**: `consent-report.js` + `run-consent-report.js`

**Funcionalidad**:
- 📊 Estadísticas generales del sistema
- 👥 Lista de usuarios con y sin consentimientos
- 📈 Distribución de consentimientos por tipo
- 💡 Recomendaciones automáticas

**Ejemplo de salida**:
```
═══════════════════════════════════════════════════════════
                    REPORTE DE CONSENTIMIENTOS
═══════════════════════════════════════════════════════════

📈 ESTADÍSTICAS GENERALES:
   Total de usuarios: 14
   Usuarios CON consentimientos: 14
   Usuarios SIN consentimientos: 0
   Total de consentimientos: 14

📊 CONSENTIMIENTOS POR TIPO:
   registro: 14

✅ USUARIOS CON CONSENTIMIENTOS:
   ID  | Username           | Email                    | Consentimientos
   ----|--------------------|--------------------------|------------------
   1   | admin              | <EMAIL>         | registro
   ...

🎉 ¡Perfecto! Todos los usuarios tienen consentimientos.
```

### 2. Creación de Consentimientos Retroactivos

```bash
npm run consents:retroactive
```

**Archivo**: `create-consents-simple.js`

**Funcionalidad**:
- 🔍 Identifica usuarios sin consentimientos
- ✅ Crea consentimientos automáticamente
- 📅 Usa fecha de registro del usuario como fecha de aceptación
- 🏷️ Marca consentimientos como "retroactive" y "system-generated"
- 📊 Reporte detallado del proceso

**Características de los consentimientos retroactivos**:
- `ip_address`: "retroactive"
- `user_agent`: "system-generated"
- `fecha_aceptacion`: Fecha de registro del usuario
- `tipo_consentimiento`: "registro"
- `version_politica`: "1.0"
- `activo`: true

## 🔧 Configuración

### Requisitos
- Base de datos MySQL configurada
- Variables de entorno en `.env`:
  ```env
  DATABASE_HOST=localhost
  DATABASE_PORT=3306
  DATABASE_USERNAME=tu_usuario
  DATABASE_PASSWORD=tu_password
  DATABASE_NAME=tu_base_de_datos
  ```

### Dependencias
- `mysql2/promise`: Conexión a base de datos
- `dotenv`: Variables de entorno

## 📊 Estructura de Base de Datos

### Tabla: `consents`
```sql
- id (int, primary key)
- document_id (varchar, unique)
- fecha_aceptacion (datetime)
- ip_address (varchar)
- user_agent (longtext)
- version_politica (varchar)
- tipo_consentimiento (varchar)
- activo (tinyint)
- created_at (datetime)
- updated_at (datetime)
- published_at (datetime)
```

### Tabla: `consents_user_lnk`
```sql
- id (int, primary key)
- consent_id (int, foreign key)
- user_id (int, foreign key)
```

### Tabla: `up_users`
```sql
- id (int, primary key)
- username (varchar)
- email (varchar)
- created_at (datetime)
```

## 🚀 Uso Recomendado

### Flujo de Trabajo Típico

1. **Verificar estado actual**:
   ```bash
   npm run consents:report
   ```

2. **Si hay usuarios sin consentimientos**:
   ```bash
   npm run consents:retroactive
   ```

3. **Verificar resultados**:
   ```bash
   npm run consents:report
   ```

### Monitoreo Regular

- **Semanal**: Ejecutar `consents:report` para verificar estado
- **Después de migraciones**: Ejecutar ambos scripts
- **Antes de auditorías**: Generar reporte completo

## ⚠️ Consideraciones Importantes

### Seguridad
- ✅ Scripts de solo lectura para reportes
- ✅ Validación antes de crear consentimientos
- ✅ No duplica consentimientos existentes
- ✅ Manejo de errores sin interrumpir proceso

### Rendimiento
- ✅ Conexiones de base de datos optimizadas
- ✅ Consultas eficientes con JOINs
- ✅ Procesamiento por lotes
- ✅ Logs informativos del progreso

### Trazabilidad
- ✅ Logs detallados de cada operación
- ✅ Marcado claro de consentimientos retroactivos
- ✅ Preservación de fechas originales
- ✅ Reporte de errores específicos

## 🔍 Solución de Problemas

### Error: "Unknown column"
- Verificar estructura de base de datos
- Ejecutar migraciones de Strapi
- Verificar nombres de tablas y columnas

### Error: "Connection refused"
- Verificar configuración de base de datos en `.env`
- Confirmar que el servidor de BD esté corriendo
- Verificar credenciales de acceso

### Consentimientos no aparecen en Strapi
- Verificar que las relaciones estén correctamente creadas
- Confirmar que `published_at` no sea NULL
- Verificar permisos de contenido en Strapi

## 📝 Logs y Debugging

Los scripts proporcionan logs detallados:
- ✅ Conexión a base de datos
- 📊 Estadísticas de procesamiento
- ❌ Errores específicos con contexto
- 🎉 Confirmación de operaciones exitosas

---

**Mantenido por**: Equipo de Desarrollo CC Palmas  
**Última actualización**: Enero 2025  
**Versión**: 1.0
