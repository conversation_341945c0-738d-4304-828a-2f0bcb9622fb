#!/usr/bin/env node
/**
 * Script ejecutable para crear consentimientos retroactivos
 * Uso: node scripts/run-retroactive-consents.js
 */
const { createStrapi } = require("@strapi/strapi");
const { createRetroactiveConsents } = require("./create-retroactive-consents");
async function main() {
    let strapi;
    try {
        console.log("🚀 Iniciando Strapi...");
        // Inicializar Strapi
        strapi = await createStrapi().load();
        console.log("✅ Strapi inicializado correctamente");
        // Ejecutar el script de consentimientos retroactivos
        await createRetroactiveConsents(strapi);
    }
    catch (error) {
        console.error("💥 Error ejecutando el script:", error);
        process.exit(1);
    }
    finally {
        if (strapi) {
            console.log("🔄 Cerrando Strapi...");
            await strapi.destroy();
            console.log("✅ Strapi cerrado correctamente");
        }
        process.exit(0);
    }
}
// Ejecutar el script
main();
