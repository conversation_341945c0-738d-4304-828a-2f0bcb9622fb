#!/usr/bin/env node

/**
 * Script ejecutable para generar reporte de consentimientos
 * Uso: node scripts/run-consent-report.js
 */

const { generateConsentReport } = require("./consent-report");

async function main() {
  try {
    // Generar el reporte
    await generateConsentReport();
  } catch (error) {
    console.error("💥 Error ejecutando el reporte:", error);
    process.exit(1);
  }
}

// Ejecutar el script
main();
