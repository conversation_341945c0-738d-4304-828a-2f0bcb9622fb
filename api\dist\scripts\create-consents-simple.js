/**
 * Script para crear consentimientos retroactivos
 *
 * Este script identifica usuarios que no tienen consentimientos registrados
 * y les crea automáticamente un consentimiento de tipo "registro" usando
 * su fecha de registro original como fecha de aceptación.
 *
 * Características:
 * - Usa conexión directa a MySQL para mejor rendimiento
 * - No duplica consentimientos existentes
 * - Marca consentimientos como "retroactive" para trazabilidad
 * - Proporciona reporte detallado del proceso
 *
 * Uso: npm run consents:retroactive
 */
const mysql = require("mysql2/promise");
require("dotenv").config();
// Configuración de la base de datos
const dbConfig = {
    host: process.env.DATABASE_HOST || "localhost",
    port: parseInt(process.env.DATABASE_PORT, 10) || 3306,
    user: process.env.DATABASE_USERNAME,
    password: process.env.DATABASE_PASSWORD,
    database: process.env.DATABASE_NAME,
};
async function createRetroactiveConsents() {
    let connection;
    try {
        console.log("🔍 Iniciando creación de consentimientos retroactivos...");
        // Conectar a la base de datos
        connection = await mysql.createConnection(dbConfig);
        // 1. Obtener todos los usuarios
        const [users] = await connection.execute(`
      SELECT id, username, email, created_at
      FROM up_users
      ORDER BY id
    `);
        console.log(`📊 Total de usuarios encontrados: ${users.length}`);
        // 2. Obtener usuarios que ya tienen consentimientos
        const [usersWithConsents] = await connection.execute(`
      SELECT DISTINCT u.id
      FROM up_users u
      INNER JOIN consents_user_lnk cul ON u.id = cul.user_id
      INNER JOIN consents c ON cul.consent_id = c.id
      WHERE c.activo = 1
    `);
        const usersWithConsentsSet = new Set(usersWithConsents.map((u) => u.id));
        console.log(`✅ Usuarios con consentimientos existentes: ${usersWithConsentsSet.size}`);
        // 3. Filtrar usuarios que NO tienen consentimientos
        const usersWithoutConsents = users.filter((user) => !usersWithConsentsSet.has(user.id));
        console.log(`❌ Usuarios SIN consentimientos: ${usersWithoutConsents.length}`);
        if (usersWithoutConsents.length === 0) {
            console.log("🎉 Todos los usuarios ya tienen consentimientos. No hay nada que hacer.");
            return;
        }
        // 4. Crear consentimientos retroactivos
        let createdCount = 0;
        let errorCount = 0;
        for (const user of usersWithoutConsents) {
            try {
                // Generar un document_id único
                const documentId = `consent_${user.id}_${Date.now()}`;
                const fechaAceptacion = user.created_at || new Date().toISOString();
                // Insertar el consentimiento
                const [consentResult] = await connection.execute(`
          INSERT INTO consents (
            document_id,
            fecha_aceptacion,
            ip_address,
            user_agent,
            version_politica,
            tipo_consentimiento,
            activo,
            created_at,
            updated_at,
            published_at
          ) VALUES (?, ?, ?, ?, ?, ?, ?, NOW(), NOW(), NOW())
        `, [
                    documentId,
                    fechaAceptacion,
                    "retroactive",
                    "system-generated",
                    "1.0",
                    "registro",
                    1,
                ]);
                const consentId = consentResult.insertId;
                // Crear la relación en la tabla de enlace
                await connection.execute(`
          INSERT INTO consents_user_lnk (consent_id, user_id)
          VALUES (?, ?)
        `, [consentId, user.id]);
                createdCount++;
                console.log(`✅ Consentimiento creado para usuario: ${user.username} (${user.email})`);
            }
            catch (error) {
                errorCount++;
                console.error(`❌ Error al crear consentimiento para usuario ${user.username}:`, error.message);
            }
        }
        console.log("\n📈 RESUMEN:");
        console.log(`✅ Consentimientos creados exitosamente: ${createdCount}`);
        console.log(`❌ Errores: ${errorCount}`);
        console.log(`📊 Total procesados: ${usersWithoutConsents.length}`);
        console.log("🎉 Proceso completado.");
    }
    catch (error) {
        console.error("💥 Error general en el script:", error);
        throw error;
    }
    finally {
        if (connection) {
            await connection.end();
        }
    }
}
// Ejecutar el script
createRetroactiveConsents();
