/**
 * Script para crear consentimientos retroactivos para usuarios existentes
 * Este script identifica usuarios que no tienen consentimientos y les crea uno automáticamente
 */

const mysql = require("mysql2/promise");
require("dotenv").config();

// Configuración de la base de datos
const dbConfig = {
  host: process.env.DATABASE_HOST || "localhost",
  port: parseInt(process.env.DATABASE_PORT, 10) || 3306,
  user: process.env.DATABASE_USERNAME,
  password: process.env.DATABASE_PASSWORD,
  database: process.env.DATABASE_NAME,
};

const createRetroactiveConsents = async () => {
  let connection;
  try {
    console.log("🔍 Iniciando creación de consentimientos retroactivos...");

    // 1. Obtener todos los usuarios
    const users = await strapi.entityService.findMany(
      "plugin::users-permissions.user",
      {
        fields: ["id", "username", "email", "createdAt"],
        populate: {
          role: {
            fields: ["name", "type"],
          },
        },
      }
    );

    console.log(`📊 Total de usuarios encontrados: ${users.length}`);

    // 2. Obtener todos los consentimientos existentes
    const existingConsents = await strapi.entityService.findMany(
      "api::consent.consent",
      {
        fields: ["user"],
        populate: {
          user: {
            fields: ["id"],
          },
        },
      }
    );

    // 3. Crear un Set con los IDs de usuarios que ya tienen consentimientos
    const usersWithConsents = new Set(
      existingConsents.map((consent) => consent.user?.id).filter(Boolean)
    );

    console.log(
      `✅ Usuarios con consentimientos existentes: ${usersWithConsents.size}`
    );

    // 4. Filtrar usuarios que NO tienen consentimientos
    const usersWithoutConsents = users.filter(
      (user) => !usersWithConsents.has(user.id)
    );

    console.log(`❌ Usuarios SIN consentimientos: ${usersWithoutConsents.length}`);

    if (usersWithoutConsents.length === 0) {
      console.log(
        "🎉 Todos los usuarios ya tienen consentimientos. No hay nada que hacer."
      );
      return;
    }

    // 5. Crear consentimientos retroactivos
    let createdCount = 0;
    let errorCount = 0;

    for (const user of usersWithoutConsents) {
      try {
        // Usar la fecha de creación del usuario como fecha de aceptación
        const fechaAceptacion = user.createdAt || new Date().toISOString();

        await strapi.entityService.create("api::consent.consent", {
          data: {
            user: user.id,
            fecha_aceptacion: fechaAceptacion,
            ip_address: "retroactive", // Marcamos como retroactivo
            user_agent: "system-generated", // Marcamos como generado por el sistema
            version_politica: "1.0",
            tipo_consentimiento: "registro",
            activo: true,
          },
        });

        createdCount++;
        console.log(
          `✅ Consentimiento creado para usuario: ${user.username} (${user.email})`
        );
      } catch (error) {
        errorCount++;
        console.error(
          `❌ Error al crear consentimiento para usuario ${user.username}:`,
          error.message
        );
      }
    }

    console.log("\n📈 RESUMEN:");
    console.log(`✅ Consentimientos creados exitosamente: ${createdCount}`);
    console.log(`❌ Errores: ${errorCount}`);
    console.log(`📊 Total procesados: ${usersWithoutConsents.length}`);
    console.log("🎉 Proceso completado.");
  } catch (error) {
    console.error("💥 Error general en el script:", error);
    throw error;
  }
};

module.exports = {
  createRetroactiveConsents,
};
